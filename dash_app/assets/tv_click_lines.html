<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>TradingView Lightweight Charts — Click to Place Price Lines</title>
<style>
  :root { --bg:#0e1116; --panel:#161b22; --text:#c9d1d9; --muted:#8b949e; --accent:#58a6ff; }
  body { margin:0; display:grid; grid-template-columns: 1fr 300px; height:100vh; background:var(--bg); color:var(--text); font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial; }
  #chart { width:100%; height:100%; }
  aside { border-left:1px solid #222; background:var(--panel); display:flex; flex-direction:column; }
  header, .row { padding:10px 12px; border-bottom:1px solid #222; }
  button { background:#222833; color:var(--text); border:1px solid #2b3340; padding:6px 10px; border-radius:8px; cursor:pointer; }
  .muted { color:var(--muted); font-size:12px; }
  ul { list-style:none; padding:0 0 10px 0; margin:0; }
  li { display:flex; justify-content:space-between; align-items:center; gap:8px; padding:6px 8px; border:1px solid #2b3340; border-radius:8px; margin:6px 12px; }
  .pill { background:#0b1d33; border:1px solid #14304f; border-radius:999px; padding:2px 8px; }
</style>
</head>
<body>
  <div id="chart"></div>
  <aside>
    <header>
      <button id="clear">🧹 Clear lines</button>
    </header>
    <div class="row">
      <div class="muted">Click anywhere on the chart to drop a horizontal line at that price.</div>
    </div>
    <div class="row">
      <strong>Placed lines</strong>
      <ul id="list"></ul>
    </div>
  </aside>

  <!-- TradingView Lightweight Charts (MIT) -->
  <script src="https://unpkg.com/lightweight-charts@4.2.2/dist/lightweight-charts.standalone.production.js"></script>
  <script>
    // ---- Chart setup
    const chartEl = document.getElementById('chart');
    const chart = LightweightCharts.createChart(chartEl, {
      layout: { background: { color: '#0e1116' }, textColor: '#c9d1d9' },
      rightPriceScale: { borderVisible: false },
      timeScale: { borderVisible: false, timeVisible: true },
      grid: { horzLines: { color: '#1f2633' }, vertLines: { color: '#1f2633' } },
      crosshair: { mode: LightweightCharts.CrosshairMode.Normal },
    });
    const series = chart.addCandlestickSeries({
      upColor: '#26a69a', downColor: '#ef5350', wickUpColor: '#26a69a', wickDownColor: '#ef5350', borderVisible: false,
      priceFormat: { type: 'price', precision: 2, minMove: 0.01 },
    });

    // Synthetic demo data (replace with your feed if needed)
    const data = [];
    const t0 = Math.floor(Date.now()/1000) - 3600*24*30;
    let p = 100;
    for (let i=0;i<300;i++){
      const t = t0 + i*3600;
      const o = p, h = o + Math.random()*3+1, l = o - (Math.random()*3+1), c = l + Math.random()*(h-l);
      p = c;
      data.push({ time: t, open:o, high:h, low:l, close:c });
    }
    series.setData(data);
    chart.timeScale().fitContent();

    // ---- Click-to-line
    const lines = []; // {price, handle}
    const listEl = document.getElementById('list');

    function fmt(x){ return Number(x).toLocaleString(undefined, { maximumFractionDigits: 8 }); }

    function addListItem(price, handle){
      const li = document.createElement('li');
      const left = document.createElement('div');
      left.textContent = 'Line @ ';
      const pill = document.createElement('span'); pill.className='pill'; pill.textContent = fmt(price);
      left.appendChild(pill);
      const right = document.createElement('div');
      const copy = document.createElement('button'); copy.textContent='Copy';
      copy.onclick = () => { navigator.clipboard.writeText(String(price)); copy.textContent='✔'; setTimeout(()=>copy.textContent='Copy',800); };
      const del = document.createElement('button'); del.textContent='🗑️';
      del.onclick = () => {
        series.removePriceLine(handle);
        li.remove();
      };
      right.append(copy, del);
      li.append(left, right);
      listEl.appendChild(li);
    }

    chart.subscribeClick((param) => {
      // Option A (simple): use the nearest candle's close price under the crosshair
      let price = null;
      if (param && param.seriesData) {
        const v = param.seriesData.get(series);
        if (v && (typeof v.close === 'number')) price = v.close;
      }
      // Option B (pixel -> price): convert Y pixel to exact price at mouse point
      // if price == null and param.point?.y != null:
      if (price == null && param && param.point && typeof param.point.y === 'number') {
        const ps = series.priceScale();
        price = ps.coordinateToPrice(param.point.y);
      }
      if (price == null) return;

      const handle = series.createPriceLine({
        price,
        color: '#58a6ff',
        lineWidth: 2,
        lineStyle: 0,
        axisLabelVisible: true,
        title: fmt(price),
      });
      lines.push({ price, handle });
      addListItem(price, handle);

      // Optional: notify parent (e.g., a Dash app iframe host)
      window.parent?.postMessage({ type: 'lw_line_added', price }, '*');
      console.log('Line placed at', price);
    });

    // Clear all
    document.getElementById('clear').onclick = () => {
      for (const {handle} of lines) series.removePriceLine(handle);
      lines.length = 0;
      listEl.innerHTML = '';
      window.parent?.postMessage({ type: 'lw_lines_cleared' }, '*');
    };

    // Responsive
    const ro = new ResizeObserver(entries=>{
      const { width, height } = entries[0].contentRect;
      chart.applyOptions({ width, height });
    });
    ro.observe(chartEl);
  </script>
</body>
</html>
