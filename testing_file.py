
# app.py
from dash import Dash, html, dcc
app = Dash(__name__)
app.layout = html.Div(
    style={"height":"100vh","display":"grid","gridTemplateRows":"1fr auto","background":"#0e1116"},
    children=[
        html.Iframe(
            src="dash_app/assets/tv_click_lines.html",
            style={"width":"100%","height":"100%","border":"0"}
        ),
        html.Div("Prices will also log in the browser console.", style={"color":"#8b949e","padding":"6px 10px"})
    ]
)
if __name__ == "__main__":
    app.run(debug=False)


# from dash import Dash, dcc, html, Input, Output, State
#
# # 1) Define your defaults
# default_options = ["Alpha", "Bravo", "Charlie"]
#
# app = Dash(__name__)
#
# # 2) Layout: a Store + a Dropdown + a Div to show selections
# app.layout = html.Div([
#     # keep your defaults here
#     dcc.Store(id="default-options", data=default_options),
#
#     dcc.Dropdown(
#         id="dropdown",
#         options=[{"label": opt, "value": opt} for opt in default_options],
#         value=[],
#         multi=True,
#         placeholder="Search or select…",
#     ),
#
#     html.Div(id="selected-output", style={"marginTop": "1rem"})
# ])
#
#
# # 3) Callback to update the dropdown’s options
# @app.callback(
#     Output("dropdown", "options"),
#     Input("dropdown", "search_value"),
#     Input("dropdown", "value"),
#     State("default-options", "data"),
#     prevent_initial_call=True
# )
# def show_current_search_value_as_custom_dropdown_option(search_value, current_values, defaults):
#     # start with the defaults
#     new_options = [{"label": d, "value": d} for d in defaults]
#
#     # if the user’s search text isn’t already in defaults, add it
#     if search_value and search_value not in defaults:
#         new_options.append({"label": search_value, "value": search_value})
#
#     # also re-add any selected values that aren’t in defaults,
#     # so they don’t get dropped when you clear the search
#     if current_values:
#         for val in current_values:
#             if val not in defaults:
#                 new_options.append({"label": val, "value": val})
#
#     return new_options


# 4) (Optional) Show what’s selected
# @app.callback(
#     Output("selected-output", "children"),
#     Input("dropdown", "value")
# )
# def display_selected(values):
#     return f"Selected values: {values}"


if __name__ == "__main__":
    app.run(debug=False)

# import tweepy
# from solana.rpc.api import Client
#
# # Twitter API credentials (you need to fill these in)
# API_KEY = '*************************'
# API_SECRET_KEY = 'q0lKCq55nG49XAmc0H7jcn30kbuVtbs0TSagkMGFTSpItjIi3T'
# ACCESS_TOKEN = '1892653748201385984-YgncIUbPj0g9yfWeAWPJOqyII3xR6F'
# ACCESS_TOKEN_SECRET = 'YX0pjBORPfZOvTw7pHfxo2w1Rpba0gw3NEWm2czv9ZSud'
#
# # Initialize Twitter API client
# def get_twitter_api():
#     auth = tweepy.OAuth1UserHandler(API_KEY, API_SECRET_KEY, ACCESS_TOKEN, ACCESS_TOKEN_SECRET)
#     return tweepy.API(auth)
#
# # Fetch trending topics from Twitter for a given location (WOEID)
# def get_trending_topics(api, woeid=23424848):
#     try:
#         trends_result = api.available_trends()
#         # trends_result = api.
#         trending_topics = [trend['name'] for trend in trends_result[0]['trends']]
#         print("Trending topics:", trending_topics)
#         return trending_topics
#     except Exception as e:
#         print("Error fetching trending topics:", e)
#         return []
#
# # Solana RPC client setup (mainnet-beta endpoint)
# solana_client = Client("https://api.mainnet-beta.solana.com")
#
# # Example searching of tokens (pseudocode, replace with real implementation)
# def search_solana_for_memecoins(trending_topics):
#     # This part is pseudocode and should be replaced with your logic to query Solana
#     try:
#         # Assume you have a function to list recent tokens or active token metadata
#         recent_tokens = []  # List to be filled with token data
#
#         print("Searching Solana for matches...")
#         for token in recent_tokens:
#             token_name = token.get('name', '').lower()
#             for trend in trending_topics:
#                 if trend.lower() in token_name:
#                     print(f"Found memecoin {token_name} matching trend {trend}")
#     except Exception as e:
#         print("Error searching Solana:", e)
#
# def main():
#     # Get the Twitter API client
#     twitter_api = get_twitter_api()
#
#     # Fetch trending topics
#     trending_topics = get_trending_topics(twitter_api)
#
#     # Search for trending memecoins on Solana
#     # search_solana_for_memecoins(trending_topics)
#
# if __name__ == '__main__':
#     main()
#
#
# import time
#
# from services.bybit import Bybit

# import requests
# from requests_oauthlib import OAuth1
#
# # Your API keys and tokens
# API_KEY = '*************************'
# API_SECRET_KEY = 'q0lKCq55nG49XAmc0H7jcn30kbuVtbs0TSagkMGFTSpItjIi3T'
# ACCESS_TOKEN = '1892653748201385984-YgncIUbPj0g9yfWeAWPJOqyII3xR6F'
# ACCESS_TOKEN_SECRET = 'YX0pjBORPfZOvTw7pHfxo2w1Rpba0gw3NEWm2czv9ZSud'
#
# # Use OAuth1 from requests_oauthlib to sign the requests
# auth = OAuth1(API_KEY, API_SECRET_KEY, ACCESS_TOKEN, ACCESS_TOKEN_SECRET)
#
# # Example of making a request
# def call_trends_endpoint():
#     url = "https://api.x.com/2/usage/personalized_trends"
#     try:
#         response = requests.get(url, auth=auth)
#         print(response.status_code)
#         print(response.json())
#     except Exception as e:
#         print(f"Error: {e}")
#
# def main():
#     call_trends_endpoint()
#
# if __name__ == '__main__':
#     main()

############################
# import requests
#
# proxies = {
#     "http": "socks5h://**************:1080",
#     "https": "socks5h://**************:1080"
# }
#
# try:
#     response = requests.get("https://ifconfig.me", proxies=proxies, timeout=10)
#     vpn_ip = response.text.strip()
#     print(f"🛡️ Traffic is exiting via VPN: {vpn_ip}")
# except Exception as e:
#     print("❌ VPN proxy test failed:", str(e))
######################################

# 3 days ago in milliseconds
# start_time = int(time.time() - 3 * 86400) * 1000
# print(start_time)
# price = Bybit.getOrders(startTime=start_time)
#
# print(price)

# import base64
# import requests
#
# # Your credentials
# API_KEY = '*************************'
# API_SECRET_KEY = 'q0lKCq55nG49XAmc0H7jcn30kbuVtbs0TSagkMGFTSpItjIi3T'
#
# def get_bearer_token(api_key, api_secret_key):
#     bearer_token_credential = f"{api_key}:{api_secret_key}"
#     base64_bearer_token = base64.b64encode(bearer_token_credential.encode('ascii')).decode('ascii')
#
#     url = "https://api.twitter.com/oauth2/token"
#     headers = {
#         "Authorization": f"Basic {base64_bearer_token}",
#         "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8"
#     }
#     body = {
#         "grant_type": "client_credentials"
#     }
#
#     response = requests.post(url, headers=headers, data=body)
#
#     if response.status_code == 200:
#         bearer_token = response.json()['access_token']
#         print(f"Bearer Token: {bearer_token}")
#         return bearer_token
#     else:
#         print(f"Failed to get bearer token: {response.status_code}")
#         print(response.text)
#         return None
#
# def call_trends_endpoint(bearer_token):
#     url = "https://api.x.com/2/tweets"
#     headers = {
#         "Authorization": f"Bearer {bearer_token}"
#     }
#     response = requests.get(url, headers=headers)
#     print(response.status_code)
#     print(response.text)
#
# def main():
#     bearer_token = get_bearer_token(API_KEY, API_SECRET_KEY)
#     if bearer_token:
#         call_trends_endpoint(bearer_token)
#
# if __name__ == '__main__':
#     main()
